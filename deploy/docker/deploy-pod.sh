#!/bin/bash

# Refly Pod 一键部署脚本
# 将所有服务部署在一个 Podman Pod 中

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Pod 和容器名称
POD_NAME="refly-pod"
CONTAINERS=(
    "refly-db"
    "refly-redis" 
    "refly-minio"
    "refly-qdrant"
    "refly-searxng"
    "refly-api"
    "refly-web"
)

# 检查 Podman 是否安装
check_podman() {
    log_info "检查 Podman 安装状态..."
    if ! command -v podman &> /dev/null; then
        log_error "Podman 未安装，请先安装 Podman"
        exit 1
    fi
    
    PODMAN_VERSION=$(podman --version)
    log_success "Podman 已安装: $PODMAN_VERSION"
}

# 清理现有的 Pod 和容器
cleanup_existing() {
    log_info "清理现有的 Pod 和容器..."
    
    # 停止并删除现有容器
    for container in "${CONTAINERS[@]}"; do
        if podman container exists "$container" 2>/dev/null; then
            log_info "停止容器: $container"
            podman stop "$container" 2>/dev/null || true
            podman rm "$container" 2>/dev/null || true
        fi
    done
    
    # 删除现有 Pod
    if podman pod exists "$POD_NAME" 2>/dev/null; then
        log_info "删除现有 Pod: $POD_NAME"
        podman pod stop "$POD_NAME" 2>/dev/null || true
        podman pod rm "$POD_NAME" 2>/dev/null || true
    fi
    
    log_success "清理完成"
}

# 创建数据卷
create_volumes() {
    log_info "创建数据卷..."
    
    volumes=(
        "refly-db-data"
        "refly-minio-data"
        "refly-redis-data"
        "refly-qdrant-data"
    )
    
    for volume in "${volumes[@]}"; do
        if ! podman volume exists "$volume" 2>/dev/null; then
            podman volume create "$volume"
            log_info "创建卷: $volume"
        else
            log_info "卷已存在: $volume"
        fi
    done
    
    log_success "数据卷创建完成"
}

# 创建 Pod
create_pod() {
    log_info "创建 Refly Pod..."
    
    podman pod create \
        --name "$POD_NAME" \
        --publish 15700:80 \
        --publish 15800:5800 \
        --publish 15801:5801 \
        --publish 15432:5432 \
        --publish 16379:6379 \
        --publish 18001:8001 \
        --publish 19000:9000 \
        --publish 19001:9001 \
        --publish 16333:6333 \
        --publish 16334:6334 \
        --publish 18080:8080
    
    log_success "Pod 创建成功: $POD_NAME"
}

# 启动数据库容器
start_database() {
    log_info "启动 PostgreSQL 数据库..."
    
    podman run -d \
        --pod "$POD_NAME" \
        --name "refly-db" \
        --volume "refly-db-data:/var/lib/postgresql/data:Z" \
        --env POSTGRES_DB=refly \
        --env POSTGRES_USER=refly \
        --env POSTGRES_PASSWORD=test \
        docker.io/library/postgres:16-alpine
    
    log_success "PostgreSQL 启动成功"
}

# 启动 Redis 容器
start_redis() {
    log_info "启动 Redis 缓存..."
    
    podman run -d \
        --pod "$POD_NAME" \
        --name "refly-redis" \
        --volume "refly-redis-data:/data:Z" \
        docker.io/redis/redis-stack:latest
    
    log_success "Redis 启动成功"
}

# 启动 MinIO 容器
start_minio() {
    log_info "启动 MinIO 对象存储..."
    
    podman run -d \
        --pod "$POD_NAME" \
        --name "refly-minio" \
        --volume "refly-minio-data:/data:Z" \
        --env MINIO_ROOT_USER=minioadmin \
        --env MINIO_ROOT_PASSWORD=minioadmin \
        docker.io/minio/minio:RELEASE.2025-01-20T14-49-07Z \
        server /data --console-address ":9001"
    
    log_success "MinIO 启动成功"
}

# 启动 Qdrant 容器
start_qdrant() {
    log_info "启动 Qdrant 向量数据库..."
    
    podman run -d \
        --pod "$POD_NAME" \
        --name "refly-qdrant" \
        --volume "refly-qdrant-data:/qdrant/storage:Z" \
        docker.io/reflyai/qdrant:v1.13.1
    
    log_success "Qdrant 启动成功"
}

# 启动 SearXNG 容器
start_searxng() {
    log_info "启动 SearXNG 搜索引擎..."
    
    # 创建 SearXNG 配置目录
    mkdir -p ./searxng
    
    podman run -d \
        --pod "$POD_NAME" \
        --name "refly-searxng" \
        --volume "./searxng:/etc/searxng:Z" \
        --env SEARXNG_BASE_URL=https://localhost/ \
        --env UWSGI_WORKERS=4 \
        --env UWSGI_THREADS=4 \
        docker.io/searxng/searxng:latest
    
    log_success "SearXNG 启动成功"
}

# 等待服务就绪
wait_for_services() {
    log_info "等待基础服务就绪..."
    
    # 等待数据库
    log_info "等待 PostgreSQL 就绪..."
    for i in {1..30}; do
        if podman exec refly-db pg_isready -U refly >/dev/null 2>&1; then
            log_success "PostgreSQL 已就绪"
            break
        fi
        if [ $i -eq 30 ]; then
            log_error "PostgreSQL 启动超时"
            exit 1
        fi
        sleep 2
    done
    
    # 等待 Redis
    log_info "等待 Redis 就绪..."
    for i in {1..20}; do
        if podman exec refly-redis redis-cli ping >/dev/null 2>&1; then
            log_success "Redis 已就绪"
            break
        fi
        if [ $i -eq 20 ]; then
            log_error "Redis 启动超时"
            exit 1
        fi
        sleep 2
    done
    
    # 等待 MinIO
    log_info "等待 MinIO 就绪..."
    for i in {1..20}; do
        if curl -f http://localhost:19000/minio/health/live >/dev/null 2>&1; then
            log_success "MinIO 已就绪"
            break
        fi
        if [ $i -eq 20 ]; then
            log_warning "MinIO 可能需要更多时间启动"
            break
        fi
        sleep 3
    done
}

# 启动 API 服务
start_api() {
    log_info "启动 Refly API 服务..."
    
    podman run -d \
        --pod "$POD_NAME" \
        --name "refly-api" \
        --env NODE_ENV=production \
        --env PORT=5800 \
        --env WS_PORT=5801 \
        --env ORIGIN=http://localhost:15700 \
        --env AUTO_MIGRATE_DB_SCHEMA=1 \
        --env DATABASE_URL=postgresql://refly:test@localhost:5432/refly?schema=refly \
        --env REDIS_HOST=localhost \
        --env REDIS_PORT=6379 \
        --env MINIO_INTERNAL_ENDPOINT=localhost \
        --env MINIO_EXTERNAL_ENDPOINT=localhost \
        --env MINIO_INTERNAL_ACCESS_KEY=minioadmin \
        --env MINIO_INTERNAL_SECRET_KEY=minioadmin \
        --env MINIO_EXTERNAL_ACCESS_KEY=minioadmin \
        --env MINIO_EXTERNAL_SECRET_KEY=minioadmin \
        --env QDRANT_HOST=localhost \
        --env SEARXNG_BASE_URL=http://localhost:8080 \
        --env JWT_SECRET=your-super-secret-jwt-key-change-this-in-production \
        --env ENCRYPTION_KEY=0123456789abcdef0123456789abcdef0123456789abcdef0123456789abcdef \
        --env AUTH_SKIP_VERIFICATION=true \
        --env EMAIL_AUTH_ENABLED=true \
        --env RESEND_API_KEY=re_123 \
        docker.io/reflyai/refly-api:latest
    
    log_success "API 服务启动成功"
}

# 启动 Web 服务
start_web() {
    log_info "启动 Refly Web 前端..."

    # 确保 nginx 配置文件存在
    if [ ! -f "./nginx-pod.conf" ]; then
        log_error "nginx-pod.conf 文件不存在"
        exit 1
    fi

    podman run -d \
        --pod "$POD_NAME" \
        --name "refly-web" \
        --volume "./nginx-pod.conf:/etc/nginx/conf.d/default.conf:Z" \
        --env API_URL=/api \
        --env COLLAB_URL=/collab \
        --env STATIC_PUBLIC_ENDPOINT=/api/v1/misc/public \
        --env STATIC_PRIVATE_ENDPOINT=/api/v1/misc \
        docker.io/reflyai/refly-web:latest

    log_success "Web 前端启动成功"
}

# 等待应用服务就绪
wait_for_app_services() {
    log_info "等待应用服务就绪..."
    
    # 等待 API 服务
    log_info "等待 API 服务就绪..."
    for i in {1..60}; do
        if curl -f http://localhost:15800 >/dev/null 2>&1; then
            log_success "API 服务已就绪"
            break
        fi
        if [ $i -eq 60 ]; then
            log_error "API 服务启动超时"
            exit 1
        fi
        sleep 3
    done
    
    # 等待 Web 服务
    log_info "等待 Web 服务就绪..."
    for i in {1..30}; do
        if curl -f http://localhost:15700 >/dev/null 2>&1; then
            log_success "Web 服务已就绪"
            break
        fi
        if [ $i -eq 30 ]; then
            log_error "Web 服务启动超时"
            exit 1
        fi
        sleep 2
    done
}

# 显示部署状态
show_status() {
    log_info "Pod 和容器状态："
    podman pod ps
    echo ""
    podman ps --filter pod="$POD_NAME"
    
    echo ""
    log_success "🎉 Refly Pod 部署完成！"
    echo ""
    log_info "📋 访问地址："
    log_info "  🌐 Web 界面: http://localhost:15700"
    log_info "  🔧 API 服务: http://localhost:15800"
    log_info "  📦 MinIO 控制台: http://localhost:19001 (minioadmin/minioadmin)"
    log_info "  🗄️  Redis 管理: http://localhost:18001"
    log_info "  🔍 SearXNG: http://localhost:18080"
    echo ""
    log_info "📊 管理命令："
    log_info "  查看状态: podman pod ps"
    log_info "  查看日志: podman logs refly-api"
    log_info "  停止 Pod: podman pod stop $POD_NAME"
    log_info "  启动 Pod: podman pod start $POD_NAME"
    log_info "  删除 Pod: podman pod rm $POD_NAME"
}

# 主函数
main() {
    log_info "🚀 开始 Refly Pod 部署..."
    
    check_podman
    cleanup_existing
    create_volumes
    create_pod
    start_database
    start_redis
    start_minio
    start_qdrant
    start_searxng
    wait_for_services
    start_api
    start_web
    wait_for_app_services
    show_status
    
    log_success "✅ Refly Pod 部署完成！"
}

# 如果直接运行此脚本
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
