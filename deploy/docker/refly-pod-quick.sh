#!/bin/bash

# Refly Pod 快速管理脚本
# 一键启动/停止/重启 Refly Pod

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

POD_NAME="refly-pod"

# 显示使用帮助
show_help() {
    echo "🚀 Refly Pod 快速管理工具"
    echo ""
    echo "用法: $0 [命令]"
    echo ""
    echo "命令:"
    echo "  start    启动 Refly Pod"
    echo "  stop     停止 Refly Pod"
    echo "  restart  重启 Refly Pod"
    echo "  status   查看 Pod 状态"
    echo "  open     在浏览器中打开 Web 界面"
    echo "  help     显示此帮助信息"
    echo ""
    echo "如果不提供命令，将显示当前状态并提供交互式选择。"
}

# 检查 Pod 是否存在
check_pod_exists() {
    if ! podman pod exists "$POD_NAME" 2>/dev/null; then
        log_error "Pod '$POD_NAME' 不存在！"
        log_info "请先运行部署脚本: ./deploy-pod.sh"
        exit 1
    fi
}

# 获取 Pod 状态
get_pod_status() {
    if podman pod exists "$POD_NAME" 2>/dev/null; then
        podman pod ps --filter name="$POD_NAME" --format "{{.Status}}" | head -1
    else
        echo "不存在"
    fi
}

# 启动 Pod
start_pod() {
    check_pod_exists
    
    local status=$(get_pod_status)
    if [ "$status" = "Running" ]; then
        log_info "Pod 已经在运行中"
        return 0
    fi
    
    log_info "启动 Refly Pod..."
    podman pod start "$POD_NAME"
    
    # 等待服务就绪
    log_info "等待服务启动..."
    sleep 5
    
    for i in {1..30}; do
        if curl -f http://localhost:15700 >/dev/null 2>&1; then
            log_success "✅ Refly Pod 启动成功！"
            log_info "🌐 Web 界面: http://localhost:15700"
            return 0
        fi
        sleep 2
    done
    
    log_warning "Pod 已启动，但 Web 服务可能需要更多时间"
}

# 停止 Pod
stop_pod() {
    check_pod_exists
    
    local status=$(get_pod_status)
    if [ "$status" != "Running" ]; then
        log_info "Pod 已经停止"
        return 0
    fi
    
    log_info "停止 Refly Pod..."
    podman pod stop "$POD_NAME"
    log_success "✅ Refly Pod 已停止"
}

# 重启 Pod
restart_pod() {
    log_info "重启 Refly Pod..."
    stop_pod
    sleep 3
    start_pod
}

# 显示状态
show_status() {
    if podman pod exists "$POD_NAME" 2>/dev/null; then
        local status=$(get_pod_status)
        echo ""
        log_info "📦 Pod 状态: $status"
        
        if [ "$status" = "Running" ]; then
            echo ""
            log_info "🌐 服务访问地址："
            echo "  Web 界面: http://localhost:15700"
            echo "  API 服务: http://localhost:15800"
            echo "  MinIO 控制台: http://localhost:19001"
            echo "  Redis 管理: http://localhost:18001"
            echo ""
            
            # 快速健康检查
            log_info "🔍 服务状态："
            if curl -f http://localhost:15700 >/dev/null 2>&1; then
                echo "  ✅ Web 界面: 正常"
            else
                echo "  ❌ Web 界面: 异常"
            fi
            
            if curl -f http://localhost:15800 >/dev/null 2>&1; then
                echo "  ✅ API 服务: 正常"
            else
                echo "  ❌ API 服务: 异常"
            fi
        fi
    else
        log_warning "Pod 不存在，请先运行: ./deploy-pod.sh"
    fi
}

# 在浏览器中打开
open_browser() {
    check_pod_exists
    
    local status=$(get_pod_status)
    if [ "$status" != "Running" ]; then
        log_error "Pod 未运行，请先启动: $0 start"
        exit 1
    fi
    
    log_info "在浏览器中打开 Refly..."
    if command -v open >/dev/null 2>&1; then
        # macOS
        open http://localhost:15700
    elif command -v xdg-open >/dev/null 2>&1; then
        # Linux
        xdg-open http://localhost:15700
    else
        log_info "请手动打开: http://localhost:15700"
    fi
}

# 交互式菜单
interactive_menu() {
    while true; do
        echo ""
        echo "🚀 Refly Pod 管理"
        echo "=================="
        show_status
        echo ""
        echo "请选择操作："
        echo "1) 启动 Pod"
        echo "2) 停止 Pod"
        echo "3) 重启 Pod"
        echo "4) 刷新状态"
        echo "5) 打开 Web 界面"
        echo "6) 查看详细状态"
        echo "0) 退出"
        echo ""
        read -p "请输入选择 (0-6): " choice
        
        case $choice in
            1)
                start_pod
                ;;
            2)
                stop_pod
                ;;
            3)
                restart_pod
                ;;
            4)
                # 刷新状态，继续循环
                ;;
            5)
                open_browser
                ;;
            6)
                ./manage-pod.sh status
                ;;
            0)
                log_info "再见！"
                exit 0
                ;;
            *)
                log_error "无效选择，请输入 0-6"
                ;;
        esac
    done
}

# 主函数
main() {
    case "${1:-interactive}" in
        start)
            start_pod
            ;;
        stop)
            stop_pod
            ;;
        restart)
            restart_pod
            ;;
        status)
            show_status
            ;;
        open)
            open_browser
            ;;
        help|--help|-h)
            show_help
            ;;
        interactive)
            interactive_menu
            ;;
        *)
            log_error "未知命令: $1"
            echo ""
            show_help
            exit 1
            ;;
    esac
}

# 如果直接运行此脚本
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
